#!/usr/bin/env python3
"""
Debug script to test the action type determination logic
"""

import sys
import os

# Add the app directory to the path
sys.path.insert(0, 'app')

def test_action_type_logic():
    """Test the action type determination logic directly"""
    
    # Test the exact data from the JSON
    test_action = {"name": "cleanupSteps action"}
    
    print(f"Testing action: {test_action}")
    
    # Simulate the logic from _determine_action_type
    action_type = test_action.get('type',
                 test_action.get('action_type',
                 test_action.get('actionType', '')))
    
    print(f"Initial action_type from fields: '{action_type}'")
    
    if not action_type:
        description = test_action.get('description', test_action.get('name', '')).lower()
        print(f"Description (lowercase): '{description}'")
        
        if 'info action' in description:
            print("Matched: info action")
            action_type = 'info'
        elif 'cleanupsteps action' in description:
            print("Matched: cleanupsteps action")
            action_type = 'cleanupSteps'
        elif 'tap' in description or 'click' in description:
            print("Matched: tap/click")
            action_type = 'tap'
        elif 'launch' in description:
            print("Matched: launch")
            action_type = 'launchApp'
        else:
            print("No match found, defaulting to 'action'")
            action_type = 'action'
    
    print(f"Final action_type: '{action_type}'")
    
    # Test the type mapping
    type_mapping = {
        'tap': 'tap',
        'swipe': 'swipe',
        'text': 'text',
        'type': 'text',
        'wait': 'wait',
        'launch': 'launchApp',
        'terminate': 'terminateApp',
        'click': 'tap',
        'addlog': 'addLog',
        'takescreenshot': 'takeScreenshot',
        'info': 'info',
        'cleanupsteps': 'cleanupSteps'
    }
    
    final_type = type_mapping.get(action_type, action_type)
    print(f"Mapped type: '{final_type}'")

if __name__ == "__main__":
    test_action_type_logic()
